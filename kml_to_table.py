#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KML文件转换为表格工具
将KML文件中的层级结构和地理信息转换为表格形式
"""

import xml.etree.ElementTree as ET
import pandas as pd
import re
from typing import List, Dict, Any

def parse_kml_to_table(kml_file_path: str) -> pd.DataFrame:
    """
    解析KML文件并转换为表格形式
    
    Args:
        kml_file_path: KML文件路径
        
    Returns:
        包含层级结构和地理信息的DataFrame
    """
    # 解析XML文件
    tree = ET.parse(kml_file_path)
    root = tree.getroot()
    
    # 定义命名空间
    ns = {'kml': 'http://www.opengis.net/kml/2.2'}
    
    # 存储结果的列表
    results = []
    
    def extract_coordinates(point_element):
        """提取坐标信息"""
        if point_element is not None:
            coords_elem = point_element.find('.//kml:coordinates', ns)
            if coords_elem is not None and coords_elem.text:
                coords = coords_elem.text.strip().split(',')
                if len(coords) >= 2:
                    try:
                        longitude = float(coords[0])
                        latitude = float(coords[1])
                        altitude = float(coords[2]) if len(coords) > 2 else 0.0
                        return longitude, latitude, altitude
                    except ValueError:
                        pass
        return None, None, None
    
    def process_folder(folder, parent_path="", level=0):
        """递归处理文件夹"""
        # 获取文件夹名称
        name_elem = folder.find('kml:name', ns)
        folder_name = name_elem.text if name_elem is not None else "未命名文件夹"
        
        # 构建当前路径
        current_path = f"{parent_path}/{folder_name}" if parent_path else folder_name
        
        # 添加文件夹信息到结果
        results.append({
            '层级': level,
            '类型': '文件夹',
            '名称': folder_name,
            '完整路径': current_path,
            '描述': '',
            '经度': None,
            '纬度': None,
            '海拔': None,
            '样式ID': None
        })
        
        # 处理子文件夹
        for subfolder in folder.findall('kml:Folder', ns):
            process_folder(subfolder, current_path, level + 1)
        
        # 处理地标
        for placemark in folder.findall('kml:Placemark', ns):
            process_placemark(placemark, current_path, level + 1)
    
    def process_placemark(placemark, parent_path="", level=0):
        """处理地标"""
        # 获取地标名称
        name_elem = placemark.find('kml:name', ns)
        placemark_name = name_elem.text if name_elem is not None else "未命名地标"
        
        # 清理名称中的CDATA
        placemark_name = re.sub(r'<!\[CDATA\[(.*?)\]\]>', r'\1', placemark_name)
        
        # 获取描述
        desc_elem = placemark.find('kml:description', ns)
        description = desc_elem.text if desc_elem is not None and desc_elem.text else ""
        
        # 获取样式URL
        style_elem = placemark.find('kml:styleUrl', ns)
        style_id = style_elem.text.replace('#', '') if style_elem is not None and style_elem.text else None
        
        # 获取坐标
        point_elem = placemark.find('kml:Point', ns)
        longitude, latitude, altitude = extract_coordinates(point_elem)
        
        # 构建完整路径
        full_path = f"{parent_path}/{placemark_name}" if parent_path else placemark_name
        
        # 添加地标信息到结果
        results.append({
            '层级': level,
            '类型': '地标',
            '名称': placemark_name,
            '完整路径': full_path,
            '描述': description,
            '经度': longitude,
            '纬度': latitude,
            '海拔': altitude,
            '样式ID': style_id
        })
    
    # 查找根文档
    document = root.find('.//kml:Document', ns)
    if document is not None:
        # 处理根级别的文件夹
        for folder in document.findall('kml:Folder', ns):
            process_folder(folder)
        
        # 处理根级别的地标
        for placemark in document.findall('kml:Placemark', ns):
            process_placemark(placemark)
    
    # 转换为DataFrame
    df = pd.DataFrame(results)
    
    return df

def save_to_excel(df: pd.DataFrame, output_file: str):
    """保存到Excel文件"""
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存完整数据
        df.to_excel(writer, sheet_name='完整数据', index=False)
        
        # 创建仅包含地标的工作表
        landmarks_df = df[df['类型'] == '地标'].copy()
        landmarks_df.to_excel(writer, sheet_name='地标数据', index=False)
        
        # 创建层级统计
        level_stats = df.groupby(['层级', '类型']).size().reset_index(name='数量')
        level_stats.to_excel(writer, sheet_name='层级统计', index=False)
        
        # 创建地区统计（基于路径分析）
        if not landmarks_df.empty:
            landmarks_df['省市'] = landmarks_df['完整路径'].str.split('/').str[2]
            region_stats = landmarks_df.groupby('省市').size().reset_index(name='地标数量')
            region_stats = region_stats.sort_values('地标数量', ascending=False)
            region_stats.to_excel(writer, sheet_name='地区统计', index=False)

def save_to_csv(df: pd.DataFrame, output_file: str):
    """保存到CSV文件"""
    df.to_csv(output_file, index=False, encoding='utf-8-sig')

def main():
    """主函数"""
    kml_file = "Map.kml"
    
    print("正在解析KML文件...")
    df = parse_kml_to_table(kml_file)
    
    print(f"解析完成！共找到 {len(df)} 个项目")
    print(f"其中文件夹: {len(df[df['类型'] == '文件夹'])} 个")
    print(f"其中地标: {len(df[df['类型'] == '地标'])} 个")
    
    # 保存到Excel
    excel_file = "kml_data.xlsx"
    print(f"正在保存到Excel文件: {excel_file}")
    save_to_excel(df, excel_file)
    
    # 保存到CSV
    csv_file = "kml_data.csv"
    print(f"正在保存到CSV文件: {csv_file}")
    save_to_csv(df, csv_file)
    
    # 显示前几行数据
    print("\n前10行数据预览:")
    print(df.head(10).to_string())
    
    # 显示层级结构统计
    print("\n层级结构统计:")
    level_stats = df.groupby(['层级', '类型']).size().reset_index(name='数量')
    print(level_stats.to_string(index=False))

if __name__ == "__main__":
    main()
